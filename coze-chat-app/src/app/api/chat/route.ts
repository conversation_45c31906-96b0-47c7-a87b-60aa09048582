import { NextRequest, NextResponse } from 'next/server';
import { CozeApiClient } from '@/lib/coze-api';
import { getBotById } from '@/lib/bots';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { message, botId, chatHistory = [] } = await request.json();

    if (!message || !botId) {
      return NextResponse.json(
        { error: 'Message and botId are required' },
        { status: 400 }
      );
    }

    // 获取bot配置
    const bot = getBotById(botId);
    if (!bot) {
      return NextResponse.json(
        { error: 'Bot not found' },
        { status: 404 }
      );
    }

    if (!bot.apiKey || !bot.botId) {
      return NextResponse.json(
        { error: 'Bot configuration incomplete' },
        { status: 500 }
      );
    }

    // 创建Coze API客户端
    const cozeClient = new CozeApiClient(bot.apiKey);

    // 准备API请求
    const apiRequest = {
      bot_id: bot.botId,
      user: uuidv4(), // 生成唯一用户ID
      query: message,
      chat_history: chatHistory.map((msg: any) => ({
        role: msg.role,
        content: msg.content,
      })),
      stream: false,
    };

    // 调用Coze API
    const response = await cozeClient.sendMessage(apiRequest);

    if (response.code !== 0) {
      throw new Error(response.msg || 'Coze API returned error');
    }

    // 提取回复内容
    const botMessage = response.messages.find(
      msg => msg.role === 'assistant' && msg.type === 'answer'
    );

    if (!botMessage) {
      throw new Error('No valid response from bot');
    }

    return NextResponse.json({
      success: true,
      message: botMessage.content,
      conversationId: response.conversation_id,
    });

  } catch (error) {
    console.error('Chat API Error:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        success: false 
      },
      { status: 500 }
    );
  }
}
