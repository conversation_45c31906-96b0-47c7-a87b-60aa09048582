'use client';

import React, { useState } from 'react';
import { BotSelector } from '@/components/BotSelector';
import { ChatInterface } from '@/components/ChatInterface';
import { BOTS, getBotById } from '@/lib/bots';
import { BotConfig } from '@/types';

export default function Home() {
  const [selectedBotId, setSelectedBotId] = useState<string>(BOTS[0]?.id || '');
  const [chatHistory, setChatHistory] = useState<Array<{role: string, content: string}>>([]);

  const selectedBot = getBotById(selectedBotId);

  const handleBotSelect = (botId: string) => {
    setSelectedBotId(botId);
    // 切换bot时清空聊天历史
    setChatHistory([]);
  };

  const handleSendMessage = async (message: string, botId: string): Promise<string> => {
    try {
      // 更新聊天历史
      const newHistory = [
        ...chatHistory,
        { role: 'user', content: message }
      ];

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          botId,
          chatHistory: newHistory,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get response');
      }

      // 更新聊天历史包含bot回复
      setChatHistory([
        ...newHistory,
        { role: 'assistant', content: data.message }
      ]);

      return data.message;
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  };

  if (!selectedBot) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">配置错误</h1>
          <p className="text-gray-600">请检查bot配置</p>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-container flex flex-col">
      <BotSelector
        bots={BOTS}
        selectedBotId={selectedBotId}
        onBotSelect={handleBotSelect}
      />
      <ChatInterface
        selectedBot={selectedBot}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
}
