// Bot configuration types
export interface BotConfig {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  botId: string;
  apiKey: string;
}

// Message types
export interface Message {
  id: string;
  type: 'text' | 'image' | 'system';
  content: {
    text?: string;
    image?: string;
  };
  user?: {
    avatar?: string;
    name?: string;
  };
  createdAt: number;
  position?: 'left' | 'right';
}

// Chat types
export interface ChatSession {
  id: string;
  botId: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
}

// API types
export interface CozeApiRequest {
  bot_id: string;
  user: string;
  query: string;
  chat_history?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  stream?: boolean;
}

export interface CozeApiResponse {
  messages: Array<{
    id: string;
    role: 'user' | 'assistant';
    type: 'answer' | 'function_call' | 'tool_output' | 'follow_up';
    content: string;
    content_type: 'text' | 'object_string' | 'card';
  }>;
  conversation_id: string;
  code: number;
  msg: string;
}
