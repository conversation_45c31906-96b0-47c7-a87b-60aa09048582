'use client';

import React from 'react';
import { BotConfig } from '@/types';

interface BotSelectorProps {
  bots: BotConfig[];
  selectedBotId: string;
  onBotSelect: (botId: string) => void;
}

export const BotSelector: React.FC<BotSelectorProps> = ({
  bots,
  selectedBotId,
  onBotSelect,
}) => {
  return (
    <div className="bot-selector p-4 border-b">
      <h3 className="text-lg font-semibold text-gray-800 mb-3">选择AI助手</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {bots.map((bot) => (
          <div
            key={bot.id}
            className={`bot-card p-4 rounded-lg cursor-pointer border ${
              selectedBotId === bot.id
                ? 'active border-primary-500'
                : 'border-gray-200 hover:border-primary-300'
            }`}
            onClick={() => onBotSelect(bot.id)}
          >
            <div className="flex items-center space-x-3">
              <div className="text-2xl">{bot.avatar}</div>
              <div className="flex-1">
                <h4 className={`font-medium ${
                  selectedBotId === bot.id ? 'text-white' : 'text-gray-800'
                }`}>
                  {bot.name}
                </h4>
                <p className={`text-sm ${
                  selectedBotId === bot.id ? 'text-blue-100' : 'text-gray-600'
                }`}>
                  {bot.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
