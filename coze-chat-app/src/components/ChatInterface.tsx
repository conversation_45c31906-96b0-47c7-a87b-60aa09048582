'use client';

import React, { useState, useEffect, useRef } from 'react';
import Chat, { Bubble, useMessages } from '@chatui/core';
import { Message, BotConfig } from '@/types';
import { v4 as uuidv4 } from 'uuid';

interface ChatInterfaceProps {
  selectedBot: BotConfig;
  onSendMessage: (message: string, botId: string) => Promise<string>;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  selectedBot,
  onSendMessage,
}) => {
  const { messages, appendMsg, setTyping } = useMessages([]);
  const [isLoading, setIsLoading] = useState(false);
  const chatRef = useRef<any>(null);

  // 当切换bot时清空消息
  useEffect(() => {
    // 清空消息历史
    messages.splice(0, messages.length);
    
    // 添加欢迎消息
    appendMsg({
      type: 'text',
      content: { text: `你好！我是${selectedBot.name}，${selectedBot.description}。有什么可以帮助您的吗？` },
      user: {
        avatar: selectedBot.avatar,
        name: selectedBot.name,
      },
    });
  }, [selectedBot.id]);

  const handleSend = async (type: string, val: string) => {
    if (type === 'text' && val.trim()) {
      // 添加用户消息
      appendMsg({
        type: 'text',
        content: { text: val },
        position: 'right',
      });

      setIsLoading(true);
      setTyping(true);

      try {
        // 调用API获取回复
        const response = await onSendMessage(val, selectedBot.id);
        
        // 添加bot回复
        appendMsg({
          type: 'text',
          content: { text: response },
          user: {
            avatar: selectedBot.avatar,
            name: selectedBot.name,
          },
        });
      } catch (error) {
        console.error('发送消息失败:', error);
        
        // 添加错误消息
        appendMsg({
          type: 'text',
          content: { 
            text: '抱歉，我现在无法回复您的消息。请稍后再试。' 
          },
          user: {
            avatar: '❌',
            name: '系统',
          },
        });
      } finally {
        setIsLoading(false);
        setTyping(false);
      }
    }
  };

  const renderMessageContent = (msg: any) => {
    const { content } = msg;
    return <Bubble content={content.text} />;
  };

  return (
    <div className="flex-1 flex flex-col">
      <div className="bg-white border-b px-4 py-3 flex items-center space-x-3">
        <div className="text-2xl">{selectedBot.avatar}</div>
        <div>
          <h2 className="font-semibold text-gray-800">{selectedBot.name}</h2>
          <p className="text-sm text-gray-600">{selectedBot.description}</p>
        </div>
      </div>
      
      <div className="flex-1">
        <Chat
          ref={chatRef}
          navbar={{ title: '' }}
          messages={messages}
          renderMessageContent={renderMessageContent}
          onSend={handleSend}
          placeholder="输入消息..."
          locale="zh-CN"
        />
      </div>
    </div>
  );
};
