import axios from 'axios';
import { CozeApiRequest, CozeApiResponse } from '@/types';

const COZE_API_BASE_URL = 'https://api.coze.com/open_api/v2/chat';

export class CozeApiClient {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async sendMessage(request: CozeApiRequest): Promise<CozeApiResponse> {
    try {
      const response = await axios.post(COZE_API_BASE_URL, request, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        timeout: 30000, // 30 seconds timeout
      });

      return response.data;
    } catch (error) {
      console.error('Coze API Error:', error);
      
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.msg || error.message;
        
        throw new Error(`Coze API Error (${status}): ${message}`);
      }
      
      throw new Error('Failed to communicate with Coze API');
    }
  }

  async streamMessage(request: CozeApiRequest): Promise<ReadableStream> {
    try {
      const response = await fetch(COZE_API_BASE_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
          ...request,
          stream: true,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.body!;
    } catch (error) {
      console.error('Coze Stream API Error:', error);
      throw new Error('Failed to establish stream connection with Coze API');
    }
  }
}
