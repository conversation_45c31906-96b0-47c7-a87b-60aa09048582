import { BotConfig } from '@/types';

// Bot configurations
export const BOTS: BotConfig[] = [
  {
    id: 'bot1',
    name: '智能助手',
    description: '通用AI助手，可以回答各种问题',
    avatar: '🤖',
    botId: process.env.COZE_BOT_1_ID || '',
    apiKey: process.env.COZE_BOT_1_API_KEY || '',
  },
  {
    id: 'bot2', 
    name: '编程专家',
    description: '专业的编程和技术问题解答',
    avatar: '💻',
    botId: process.env.COZE_BOT_2_ID || '',
    apiKey: process.env.COZE_BOT_2_API_KEY || '',
  },
  {
    id: 'bot3',
    name: '创意写作',
    description: '帮助您进行创意写作和文案创作',
    avatar: '✍️',
    botId: process.env.COZE_BOT_3_ID || '',
    apiKey: process.env.COZE_BOT_3_API_KEY || '',
  },
];

export const getBotById = (id: string): BotConfig | undefined => {
  return BOTS.find(bot => bot.id === id);
};

export const getBotByBotId = (botId: string): BotConfig | undefined => {
  return BOTS.find(bot => bot.botId === botId);
};
