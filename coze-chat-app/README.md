# Coze Chat App

基于 ChatUI 和 Coze Bot 的智能对话应用，支持集成多个 Coze bot，用户可以在不同的 AI 助手之间切换进行对话。

## 功能特性

- 🤖 **多Bot支持**: 集成3个不同的Coze bot，每个bot有不同的专业领域
- 💬 **现代化聊天界面**: 基于ChatUI构建的美观聊天界面
- 🎨 **响应式设计**: 适配桌面端和移动端
- 🔄 **实时切换**: 用户可以随时切换不同的AI助手
- 📱 **移动友好**: 完全响应式设计，支持移动设备
- 🎯 **TypeScript**: 完整的类型支持

## 技术栈

- **前端框架**: Next.js 14 (App Router)
- **UI组件**: ChatUI Core
- **样式**: Tailwind CSS
- **语言**: TypeScript
- **API集成**: Coze API
- **状态管理**: React Hooks

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd coze-chat-app
```

### 2. 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 3. 配置环境变量

复制 `.env.example` 文件为 `.env.local`:

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，填入你的Coze bot配置:

```env
# Coze Bot 1 Configuration
COZE_BOT_1_ID=your_bot_1_id_here
COZE_BOT_1_API_KEY=your_bot_1_api_key_here

# Coze Bot 2 Configuration  
COZE_BOT_2_ID=your_bot_2_id_here
COZE_BOT_2_API_KEY=your_bot_2_api_key_here

# Coze Bot 3 Configuration
COZE_BOT_3_ID=your_bot_3_id_here
COZE_BOT_3_API_KEY=your_bot_3_api_key_here
```

### 4. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目结构

```
coze-chat-app/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API路由
│   │   │   └── chat/       # 聊天API
│   │   ├── globals.css     # 全局样式
│   │   ├── layout.tsx      # 根布局
│   │   └── page.tsx        # 主页面
│   ├── components/         # React组件
│   │   ├── BotSelector.tsx # Bot选择器
│   │   └── ChatInterface.tsx # 聊天界面
│   ├── lib/                # 工具库
│   │   ├── bots.ts         # Bot配置
│   │   └── coze-api.ts     # Coze API客户端
│   └── types/              # TypeScript类型定义
│       └── index.ts
├── public/                 # 静态资源
├── .env.example           # 环境变量示例
├── next.config.js         # Next.js配置
├── tailwind.config.js     # Tailwind配置
└── package.json
```

## Bot配置

在 `src/lib/bots.ts` 中配置你的bot信息:

```typescript
export const BOTS: BotConfig[] = [
  {
    id: 'bot1',
    name: '智能助手',
    description: '通用AI助手，可以回答各种问题',
    avatar: '🤖',
    botId: process.env.COZE_BOT_1_ID || '',
    apiKey: process.env.COZE_BOT_1_API_KEY || '',
  },
  // ... 更多bot配置
];
```

## API接口

### POST /api/chat

发送消息到指定的bot。

**请求体:**
```json
{
  "message": "用户消息",
  "botId": "bot1",
  "chatHistory": [
    {"role": "user", "content": "之前的消息"},
    {"role": "assistant", "content": "bot回复"}
  ]
}
```

**响应:**
```json
{
  "success": true,
  "message": "bot回复内容",
  "conversationId": "会话ID"
}
```

## 自定义样式

应用使用Tailwind CSS进行样式设计，主要颜色方案：

- **主色调**: 蓝色系 (#0ea5e9)
- **背景**: 银色渐变 (#f8fafc - #e2e8f0)
- **卡片**: 半透明白色背景，毛玻璃效果

可以在 `src/app/globals.css` 中自定义样式。

## 部署

### Vercel部署

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 配置环境变量
4. 部署

### 其他平台

构建生产版本:

```bash
npm run build
npm start
```

## 开发指南

### 添加新的Bot

1. 在环境变量中添加新的bot配置
2. 在 `src/lib/bots.ts` 中添加bot配置
3. 重启开发服务器

### 自定义消息类型

在 `src/types/index.ts` 中扩展消息类型定义。

### 修改UI样式

- 全局样式: `src/app/globals.css`
- Tailwind配置: `tailwind.config.js`
- 组件样式: 直接在组件中使用Tailwind类

## 故障排除

### 常见问题

1. **Bot不响应**: 检查API密钥和Bot ID是否正确
2. **样式问题**: 确保Tailwind CSS正确配置
3. **构建错误**: 检查TypeScript类型定义

### 调试

启用详细日志:

```bash
DEBUG=* npm run dev
```

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
