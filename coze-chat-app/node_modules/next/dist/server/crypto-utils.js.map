{"version": 3, "sources": ["../../src/server/crypto-utils.ts"], "names": ["encryptWithSecret", "decryptWithSecret", "CIPHER_ALGORITHM", "CIPHER_KEY_LENGTH", "CIPHER_IV_LENGTH", "CIPHER_TAG_LENGTH", "CIPHER_SALT_LENGTH", "PBKDF2_ITERATIONS", "secret", "data", "iv", "crypto", "randomBytes", "salt", "key", "pbkdf2Sync", "cipher", "createCipheriv", "encrypted", "<PERSON><PERSON><PERSON>", "concat", "update", "final", "tag", "getAuthTag", "toString", "encryptedData", "buffer", "from", "slice", "decipher", "createDecipheriv", "setAuthTag"], "mappings": ";;;;;;;;;;;;;;;IAagBA,iBAAiB;eAAjBA;;IA+BAC,iBAAiB;eAAjBA;;;+DA5CG;;;;;;AAEnB,cAAc;AACd,qHAAqH;AAErH,MAAMC,mBAAmB,CAAC,WAAW,CAAC,EACpCC,oBAAoB,IACpBC,mBAAmB,IACnBC,oBAAoB,IACpBC,qBAAqB;AAEvB,MAAMC,oBAAoB,OAAQ,wCAAwC;;AAEnE,SAASP,kBAAkBQ,MAAc,EAAEC,IAAY;IAC5D,MAAMC,KAAKC,eAAM,CAACC,WAAW,CAACR;IAC9B,MAAMS,OAAOF,eAAM,CAACC,WAAW,CAACN;IAEhC,qGAAqG;IACrG,MAAMQ,MAAMH,eAAM,CAACI,UAAU,CAC3BP,QACAK,MACAN,mBACAJ,mBACA,CAAC,MAAM,CAAC;IAGV,MAAMa,SAASL,eAAM,CAACM,cAAc,CAACf,kBAAkBY,KAAKJ;IAC5D,MAAMQ,YAAYC,OAAOC,MAAM,CAAC;QAACJ,OAAOK,MAAM,CAACZ,MAAM,CAAC,IAAI,CAAC;QAAGO,OAAOM,KAAK;KAAG;IAE7E,8DAA8D;IAC9D,MAAMC,MAAMP,OAAOQ,UAAU;IAE7B,OAAOL,OAAOC,MAAM,CAAC;QACnB,uBAAuB;QACvB,mHAAmH;QACnH,+DAA+D;QAC/D,4EAA4E;QAC5EP;QACAH;QACAa;QACAL;KACD,EAAEO,QAAQ,CAAC,CAAC,GAAG,CAAC;AACnB;AAEO,SAASxB,kBACdO,MAAc,EACdkB,aAAqB;IAErB,MAAMC,SAASR,OAAOS,IAAI,CAACF,eAAe,CAAC,GAAG,CAAC;IAE/C,MAAMb,OAAOc,OAAOE,KAAK,CAAC,GAAGvB;IAC7B,MAAMI,KAAKiB,OAAOE,KAAK,CACrBvB,oBACAA,qBAAqBF;IAEvB,MAAMmB,MAAMI,OAAOE,KAAK,CACtBvB,qBAAqBF,kBACrBE,qBAAqBF,mBAAmBC;IAE1C,MAAMa,YAAYS,OAAOE,KAAK,CAC5BvB,qBAAqBF,mBAAmBC;IAG1C,qGAAqG;IACrG,MAAMS,MAAMH,eAAM,CAACI,UAAU,CAC3BP,QACAK,MACAN,mBACAJ,mBACA,CAAC,MAAM,CAAC;IAGV,MAAM2B,WAAWnB,eAAM,CAACoB,gBAAgB,CAAC7B,kBAAkBY,KAAKJ;IAChEoB,SAASE,UAAU,CAACT;IAEpB,OAAOO,SAAST,MAAM,CAACH,aAAaY,SAASR,KAAK,CAAC,CAAC,IAAI,CAAC;AAC3D"}