{"version": 3, "sources": ["../../src/server/font-utils.ts"], "names": ["getFontDefinitionFromNetwork", "getFontDefinitionFromManifest", "calculateOverrideValues", "calculateSizeAdjustValues", "getFontOverrideCss", "capsizeFontsMetrics", "require", "https", "CHROME_UA", "IE_UA", "isGoogleFont", "url", "startsWith", "GOOGLE_FONT_PROVIDER", "getFontForUA", "UA", "Promise", "resolve", "reject", "rawData", "get", "headers", "res", "on", "chunk", "toString", "e", "result", "Log", "warn", "manifest", "find", "font", "content", "parseGoogleFontName", "css", "regex", "matches", "matchAll", "fontNames", "Set", "fontFamily", "replace", "add", "formatName", "str", "word", "index", "toLowerCase", "toUpperCase", "formatOverrideValue", "val", "Math", "abs", "toFixed", "fontName", "font<PERSON>ey", "fontMetrics", "category", "ascent", "descent", "lineGap", "unitsPerEm", "fallbackFont", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "name", "xWidthAvg", "mainFontAvgWidth", "fallback<PERSON>ontName", "fallbackFontMetrics", "fallbackFontAvgWidth", "sizeAdjust", "calculateOverrideCSS", "trim", "calculateSizeAdjustCSS", "useSizeAdjust", "calcFn", "fontCss", "reduce", "cssStr", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;IAkDsBA,4BAA4B;eAA5BA;;IAuBNC,6BAA6B;eAA7BA;;IAuCAC,uBAAuB;eAAvBA;;IAkBAC,yBAAyB;eAAzBA;;IA8DAC,kBAAkB;eAAlBA;;;6DAhMK;2BAKd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACP,MAAMC,sBAAsBC,QAAQ;AACpC,MAAMC,QAAQD,QAAQ;AAEtB,MAAME,YACJ;AACF,MAAMC,QAAQ;AASd,SAASC,aAAaC,GAAW;IAC/B,OAAOA,IAAIC,UAAU,CAACC,+BAAoB;AAC5C;AAEA,SAASC,aAAaH,GAAW,EAAEI,EAAU;IAC3C,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,IAAIC,UAAe;QACnBZ,MACGa,GAAG,CACFT,KACA;YACEU,SAAS;gBACP,cAAcN;YAChB;QACF,GACA,CAACO;YACCA,IAAIC,EAAE,CAAC,QAAQ,CAACC;gBACdL,WAAWK;YACb;YACAF,IAAIC,EAAE,CAAC,OAAO;gBACZN,QAAQE,QAAQM,QAAQ,CAAC;YAC3B;QACF,GAEDF,EAAE,CAAC,SAAS,CAACG;YACZR,OAAOQ;QACT;IACJ;AACF;AAEO,eAAe1B,6BACpBW,GAAW;IAEX,IAAIgB,SAAS;IACb;;;GAGC,GACD,IAAI;QACF,IAAIjB,aAAaC,MAAM;YACrBgB,UAAU,MAAMb,aAAaH,KAAKF;QACpC;QACAkB,UAAU,MAAMb,aAAaH,KAAKH;IACpC,EAAE,OAAOkB,GAAG;QACVE,KAAIC,IAAI,CACN,CAAC,sCAAsC,EAAElB,IAAI,+BAA+B,CAAC;QAE/E,OAAO;IACT;IAEA,OAAOgB;AACT;AAEO,SAAS1B,8BACdU,GAAW,EACXmB,QAAsB;QAGpBA;IADF,OACEA,EAAAA,iBAAAA,SAASC,IAAI,CAAC,CAACC;QACb,IAAIA,QAAQA,KAAKrB,GAAG,KAAKA,KAAK;YAC5B,OAAO;QACT;QACA,OAAO;IACT,uBALAmB,eAKIG,OAAO,KAAI;AAEnB;AAEA,SAASC,oBAAoBC,GAAW;IACtC,MAAMC,QAAQ;IACd,MAAMC,UAAUF,IAAIG,QAAQ,CAACF;IAC7B,MAAMG,YAAY,IAAIC;IAEtB,KAAK,IAAIR,QAAQK,QAAS;QACxB,MAAMI,aAAaT,IAAI,CAAC,EAAE,CAACU,OAAO,CAAC,gBAAgB;QACnDH,UAAUI,GAAG,CAACF;IAChB;IAEA,OAAO;WAAIF;KAAU;AACvB;AAEA,SAASK,WAAWC,GAAW;IAC7B,OAAOA,IACJH,OAAO,CAAC,uBAAuB,SAAUI,IAAI,EAAEC,KAAK;QACnD,OAAOA,UAAU,IAAID,KAAKE,WAAW,KAAKF,KAAKG,WAAW;IAC5D,GACCP,OAAO,CAAC,QAAQ;AACrB;AAEA,SAASQ,oBAAoBC,GAAW;IACtC,OAAOC,KAAKC,GAAG,CAACF,MAAM,KAAKG,OAAO,CAAC;AACrC;AAEO,SAASpD,wBAAwBqD,QAAgB;IACtD,MAAMC,UAAUZ,WAAWW;IAC3B,MAAME,cAAcpD,mBAAmB,CAACmD,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAE,GAAGL;IACzD,MAAMM,eACJL,aAAa,UAAUM,6BAAkB,GAAGC,kCAAuB;IACrEN,SAAST,oBAAoBS,SAASG;IACtCF,UAAUV,oBAAoBU,UAAUE;IACxCD,UAAUX,oBAAoBW,UAAUC;IAExC,OAAO;QACLH;QACAC;QACAC;QACAE,cAAcA,aAAaG,IAAI;IACjC;AACF;AAEO,SAAS/D,0BAA0BoD,QAAgB;IACxD,MAAMC,UAAUZ,WAAWW;IAC3B,MAAME,cAAcpD,mBAAmB,CAACmD,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEK,SAAS,EAAE,GAC/DV;IACF,MAAMW,mBAAmBD,YAAYL;IACrC,MAAMC,eACJL,aAAa,UAAUM,6BAAkB,GAAGC,kCAAuB;IACrE,MAAMI,mBAAmBzB,WAAWmB,aAAaG,IAAI;IACrD,MAAMI,sBAAsBjE,mBAAmB,CAACgE,iBAAiB;IACjE,MAAME,uBACJD,oBAAoBH,SAAS,GAAGG,oBAAoBR,UAAU;IAChE,IAAIU,aAAaL,YAAYC,mBAAmBG,uBAAuB;IAEvEZ,SAAST,oBAAoBS,SAAUG,CAAAA,aAAaU,UAAS;IAC7DZ,UAAUV,oBAAoBU,UAAWE,CAAAA,aAAaU,UAAS;IAC/DX,UAAUX,oBAAoBW,UAAWC,CAAAA,aAAaU,UAAS;IAE/D,OAAO;QACLb;QACAC;QACAC;QACAE,cAAcA,aAAaG,IAAI;QAC/BM,YAAYtB,oBAAoBsB;IAClC;AACF;AAEA,SAASC,qBAAqBzC,IAAY;IACxC,MAAMuB,WAAWvB,KAAK0C,IAAI;IAE1B,MAAM,EAAEf,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAE,GAC9C7D,wBAAwBqD;IAE1B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;kBACjB,EAAEE,aAAa;;EAE/B,CAAC;AACH;AAEA,SAASY,uBAAuB3C,IAAY;IAC1C,MAAMuB,WAAWvB,KAAK0C,IAAI;IAE1B,MAAM,EAAEf,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAES,UAAU,EAAE,GAC1DrE,0BAA0BoD;IAE5B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;mBAChB,EAAEW,WAAW;kBACd,EAAET,aAAa;;EAE/B,CAAC;AACH;AAEO,SAAS3D,mBACdO,GAAW,EACXwB,GAAW,EACXyC,gBAAgB,KAAK;IAErB,IAAI,CAAClE,aAAaC,MAAM;QACtB,OAAO;IACT;IAEA,MAAMkE,SAASD,gBAAgBD,yBAAyBF;IAExD,IAAI;QACF,MAAMlC,YAAYL,oBAAoBC;QAEtC,MAAM2C,UAAUvC,UAAUwC,MAAM,CAAC,CAACC,QAAQzB;YACxCyB,UAAUH,OAAOtB;YACjB,OAAOyB;QACT,GAAG;QAEH,OAAOF;IACT,EAAE,OAAOpD,GAAG;QACVuD,QAAQC,GAAG,CAAC,yCAAyCxD;QACrD,OAAO;IACT;AACF"}