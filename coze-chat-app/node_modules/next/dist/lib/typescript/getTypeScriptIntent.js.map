{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptIntent.ts"], "names": ["getTypeScriptIntent", "baseDir", "intentDirs", "tsconfigPath", "resolvedTsConfigPath", "path", "join", "hasTypeScriptConfiguration", "existsSync", "content", "fs", "readFile", "encoding", "then", "txt", "trim", "firstTimeSetup", "tsFilesRegex", "excludedRegex", "dir", "typescriptFiles", "recursiveReadDir", "pathnameFilter", "name", "test", "ignoreFilter", "length"], "mappings": ";;;;+BAMsBA;;;eAAAA;;;oBANqB;6DAC1B;kCACgB;;;;;;AAI1B,eAAeA,oBACpBC,OAAe,EACfC,UAAoB,EACpBC,YAAoB;IAEpB,MAAMC,uBAAuBC,aAAI,CAACC,IAAI,CAACL,SAASE;IAEhD,sEAAsE;IACtE,WAAW;IACX,MAAMI,6BAA6BC,IAAAA,cAAU,EAACJ;IAC9C,IAAIG,4BAA4B;QAC9B,MAAME,UAAU,MAAMC,YAAE,CACrBC,QAAQ,CAACP,sBAAsB;YAAEQ,UAAU;QAAO,GAClDC,IAAI,CACH,CAACC,MAAQA,IAAIC,IAAI,IACjB,IAAM;QAEV,OAAO;YAAEC,gBAAgBP,YAAY,MAAMA,YAAY;QAAK;IAC9D;IAEA,yEAAyE;IACzE,6EAA6E;IAC7E,gDAAgD;IAChD,mEAAmE;IACnE,MAAMQ,eAAe;IACrB,MAAMC,gBAAgB;IACtB,KAAK,MAAMC,OAAOjB,WAAY;QAC5B,MAAMkB,kBAAkB,MAAMC,IAAAA,kCAAgB,EAACF,KAAK;YAClDG,gBAAgB,CAACC,OAASN,aAAaO,IAAI,CAACD;YAC5CE,cAAc,CAACF,OAASL,cAAcM,IAAI,CAACD;QAC7C;QACA,IAAIH,gBAAgBM,MAAM,EAAE;YAC1B,OAAO;gBAAEV,gBAAgB;YAAK;QAChC;IACF;IAEA,OAAO;AACT"}