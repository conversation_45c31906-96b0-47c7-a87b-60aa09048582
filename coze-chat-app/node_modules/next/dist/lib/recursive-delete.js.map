{"version": 3, "sources": ["../../src/lib/recursive-delete.ts"], "names": ["recursiveDelete", "sleep", "timeout", "Promise", "resolve", "setTimeout", "unlinkPath", "p", "isDir", "t", "promises", "rmdir", "unlink", "e", "code", "isError", "dir", "exclude", "previousPath", "result", "readdir", "withFileTypes", "all", "map", "part", "absolutePath", "join", "name", "isDirectory", "isSymlink", "isSymbolicLink", "linkPath", "readlink", "stats", "stat", "isAbsolute", "dirname", "pp", "isNotExcluded", "test"], "mappings": ";;;;+BAuCsBA;;;eAAAA;;;oBAtCG;sBACiB;gEACtB;;;;;;AAEpB,MAAMC,QAAQ,CAACC,UACb,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF;AAE/C,MAAMI,aAAa,OAAOC,GAAWC,QAAQ,KAAK,EAAEC,IAAI,CAAC;IACvD,IAAI;QACF,IAAID,OAAO;YACT,MAAME,YAAQ,CAACC,KAAK,CAACJ;QACvB,OAAO;YACL,MAAMG,YAAQ,CAACE,MAAM,CAACL;QACxB;IACF,EAAE,OAAOM,GAAG;QACV,MAAMC,OAAOC,IAAAA,gBAAO,EAACF,MAAMA,EAAEC,IAAI;QACjC,IACE,AAACA,CAAAA,SAAS,WACRA,SAAS,eACTA,SAAS,WACTA,SAAS,QAAO,KAClBL,IAAI,GACJ;YACA,MAAMR,MAAMQ,IAAI;YAChB,OAAOH,WAAWC,GAAGC,OAAOC;QAC9B;QAEA,IAAIK,SAAS,UAAU;YACrB;QACF;QAEA,MAAMD;IACR;AACF;AAKO,eAAeb,gBACpB,wCAAwC,GACxCgB,GAAW,EACX,wCAAwC,GACxCC,OAAgB,EAChB,sEAAsE,GACtEC,eAAuB,EAAE;IAEzB,IAAIC;IACJ,IAAI;QACFA,SAAS,MAAMT,YAAQ,CAACU,OAAO,CAACJ,KAAK;YAAEK,eAAe;QAAK;IAC7D,EAAE,OAAOR,GAAG;QACV,IAAIE,IAAAA,gBAAO,EAACF,MAAMA,EAAEC,IAAI,KAAK,UAAU;YACrC;QACF;QACA,MAAMD;IACR;IAEA,MAAMV,QAAQmB,GAAG,CACfH,OAAOI,GAAG,CAAC,OAAOC;QAChB,MAAMC,eAAeC,IAAAA,UAAI,EAACV,KAAKQ,KAAKG,IAAI;QAExC,yCAAyC;QACzC,mDAAmD;QACnD,IAAIC,cAAcJ,KAAKI,WAAW;QAClC,MAAMC,YAAYL,KAAKM,cAAc;QAErC,IAAID,WAAW;YACb,MAAME,WAAW,MAAMrB,YAAQ,CAACsB,QAAQ,CAACP;YAEzC,IAAI;gBACF,MAAMQ,QAAQ,MAAMvB,YAAQ,CAACwB,IAAI,CAC/BC,IAAAA,gBAAU,EAACJ,YACPA,WACAL,IAAAA,UAAI,EAACU,IAAAA,aAAO,EAACX,eAAeM;gBAElCH,cAAcK,MAAML,WAAW;YACjC,EAAE,OAAM,CAAC;QACX;QAEA,MAAMS,KAAKX,IAAAA,UAAI,EAACR,cAAcM,KAAKG,IAAI;QACvC,MAAMW,gBAAgB,CAACrB,WAAW,CAACA,QAAQsB,IAAI,CAACF;QAEhD,IAAIC,eAAe;YACjB,IAAIV,aAAa;gBACf,MAAM5B,gBAAgByB,cAAcR,SAASoB;YAC/C;YACA,OAAO/B,WAAWmB,cAAc,CAACI,aAAaD;QAChD;IACF;AAEJ"}