{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "names": ["resolveThemeColor", "resolveViewport", "resolveAlternates", "resolveRobots", "resolveVerification", "resolveAppleWebApp", "resolveAppLinks", "resolveItunes", "resolveAlternateUrl", "url", "metadataBase", "pathname", "URL", "resolveAbsoluteUrlWithPathname", "themeColor", "resolveAsArrayOrUndefined", "themeColorDescriptors", "for<PERSON>ach", "descriptor", "push", "color", "media", "viewport", "resolved", "viewportKey_", "ViewPortKeys", "viewportKey", "value", "resolveUrlValuesOfObject", "obj", "result", "key", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "alternates", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "basic", "googleBot", "VerificationKeys", "verification", "res", "other", "otherKey", "otherValue", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "appLinks", "itunes", "appId", "appArgument", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IA6BaA,iBAAiB;eAAjBA;;IAiBAC,eAAe;eAAfA;;IAyEAC,iBAAiB;eAAjBA;;IA0EAC,aAAa;eAAbA;;IAUAC,mBAAmB;eAAnBA;;IAuBAC,kBAAkB;eAAlBA;;IAsBAC,eAAe;eAAfA;;IASAC,aAAa;eAAbA;;;uBArP6B;4BACK;2BAClB;AAE7B,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,QAAgB;IAEhB,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtBH,MAAM,IAAIG,IAAID,UAAUF;IAC1B;IACA,OAAOI,IAAAA,0CAA8B,EAACJ,KAAKC,cAAcC;AAC3D;AAEO,MAAMX,oBAAiD,CAACc;QAI7DC;IAHA,IAAI,CAACD,YAAY,OAAO;IACxB,MAAME,wBAAwD,EAAE;KAEhED,6BAAAA,IAAAA,gCAAyB,EAACD,gCAA1BC,2BAAuCE,OAAO,CAAC,CAACC;QAC9C,IAAI,OAAOA,eAAe,UACxBF,sBAAsBG,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BF,sBAAsBG,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOL;AACT;AAEO,MAAMf,kBAA6C,CAACqB;IACzD,IAAIC,WAAyC;IAE7C,IAAI,OAAOD,aAAa,UAAU;QAChCC,WAAWD;IACb,OAAO,IAAIA,UAAU;QACnBC,WAAW;QACX,IAAK,MAAMC,gBAAgBC,uBAAY,CAAE;YACvC,MAAMC,cAAcF;YACpB,IAAIE,eAAeJ,UAAU;gBAC3B,IAAIK,QAAQL,QAAQ,CAACI,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAWA,QAAQA,QAAQ,QAAQ;gBACxD,IAAIJ,UAAUA,YAAY;gBAC1BA,YAAY,CAAC,EAAEE,uBAAY,CAACC,YAAY,CAAC,CAAC,EAAEC,MAAM,CAAC;YACrD;QACF;IACF;IACA,OAAOJ;AACT;AAEA,SAASK,yBACPC,GAMa,EACbnB,YAA8C,EAC9CC,QAAgB;IAEhB,IAAI,CAACkB,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACC,KAAKJ,MAAM,IAAIK,OAAOC,OAAO,CAACJ,KAAM;QAC9C,IAAI,OAAOF,UAAU,YAAYA,iBAAiBf,KAAK;YACrDkB,MAAM,CAACC,IAAI,GAAG;gBACZ;oBACEtB,KAAKD,oBAAoBmB,OAAOjB,cAAcC;gBAChD;aACD;QACH,OAAO;YACLmB,MAAM,CAACC,IAAI,GAAG,EAAE;YAChBJ,yBAAAA,MAAOV,OAAO,CAAC,CAACiB,MAAMC;gBACpB,MAAM1B,MAAMD,oBAAoB0B,KAAKzB,GAAG,EAAEC,cAAcC;gBACxDmB,MAAM,CAACC,IAAI,CAACI,MAAM,GAAG;oBACnB1B;oBACA2B,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAON;AACT;AAEA,SAASO,oBACPC,eAA0E,EAC1E5B,YAAwB,EACxBC,QAAgB;IAEhB,IAAI,CAAC2B,iBAAiB,OAAO;IAE7B,MAAM7B,MACJ,OAAO6B,oBAAoB,YAAYA,2BAA2B1B,MAC9D0B,kBACAA,gBAAgB7B,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEO,MAAMT,oBAGT,CAACqC,YAAY7B,cAAc,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAAC4B,YAAY,OAAO;IAExB,MAAMC,YAAYH,oBAChBE,WAAWC,SAAS,EACpB9B,cACAC;IAEF,MAAM8B,YAAYb,yBAChBW,WAAWE,SAAS,EACpB/B,cACAC;IAEF,MAAMU,QAAQO,yBACZW,WAAWlB,KAAK,EAChBX,cACAC;IAEF,MAAM+B,QAAQd,yBACZW,WAAWG,KAAK,EAChBhC,cACAC;IAGF,MAAMmB,SAAgC;QACpCU;QACAC;QACApB;QACAqB;IACF;IAEA,OAAOZ;AACT;AAEA,MAAMa,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOV,KAAK,EAAEW,OAAO3B,IAAI,CAAC;SACzB,IAAI,OAAO0B,OAAOV,KAAK,KAAK,WAAWW,OAAO3B,IAAI,CAAC;IAExD,IAAI0B,OAAOE,MAAM,EAAED,OAAO3B,IAAI,CAAC;SAC1B,IAAI,OAAO0B,OAAOE,MAAM,KAAK,WAAWD,OAAO3B,IAAI,CAAC;IAEzD,KAAK,MAAMY,OAAOY,WAAY;QAC5B,MAAMhB,QAAQkB,MAAM,CAACd,IAAI;QACzB,IAAI,OAAOJ,UAAU,eAAeA,UAAU,OAAO;YACnDmB,OAAO3B,IAAI,CAAC,OAAOQ,UAAU,YAAYI,MAAM,CAAC,EAAEA,IAAI,CAAC,EAAEJ,MAAM,CAAC;QAClE;IACF;IAEA,OAAOmB,OAAOE,IAAI,CAAC;AACrB;AAEO,MAAM7C,gBAAyC,CAAC0C;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLI,OAAOL,mBAAmBC;QAC1BK,WACE,OAAOL,WAAW,WAAWD,mBAAmBC,OAAOK,SAAS,IAAI;IACxE;AACF;AAEA,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AAC9D,MAAM/C,sBAAqD,CAChEgD;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMtB,OAAOoB,iBAAkB;QAClC,MAAMxB,QAAQyB,YAAY,CAACrB,IAAI;QAC/B,IAAIJ,OAAO;YACT,IAAII,QAAQ,SAAS;gBACnBsB,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAazC,IAAAA,gCAAyB,EAC1CqC,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACtB,IAAI,GAAGhB,IAAAA,gCAAyB,EAACY;QAC9C;IACF;IACA,OAAO0B;AACT;AAEO,MAAMhD,qBAAmD,CAACoD;QAS3D1C;IARJ,IAAI,CAAC0C,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxC7C,6BAAAA,IAAAA,gCAAyB,EAAC0C,UAAUG,YAAY,sBAAhD7C,2BAAmD8C,GAAG,CAAC,CAAC3B,OACtD,OAAOA,SAAS,WAAW;YAAEzB,KAAKyB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACLwB,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxDtB,OAAOqB,UAAUrB,KAAK,IAAI;QAC1BwB,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF;AAEO,MAAMxD,kBAA6C,CAACyD;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMhC,OAAOgC,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAAChC,IAAI,GAAGhB,IAAAA,gCAAyB,EAACgD,QAAQ,CAAChC,IAAI;IACzD;IACA,OAAOgC;AACT;AAEO,MAAMxD,gBAGT,CAACyD,QAAQtD,cAAc,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACqD,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B1D,oBAAoBwD,OAAOE,WAAW,EAAExD,cAAcC,YACtDwD;IACN;AACF"}