{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "names": ["collectMetadata", "resolveMetadataItems", "accumulateMetadata", "resolveMetadata", "hasIconsProperty", "icons", "prop", "URL", "Array", "isArray", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "icon", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "resolveTwitter", "images", "metadataBase", "resolvedOpenGraph", "resolveOpenGraph", "merge", "key_", "key", "title", "resolveTitle", "alternates", "resolveAlternates", "verification", "resolveVerification", "viewport", "resolveViewport", "resolveIcons", "appleWebApp", "resolveAppleWebApp", "appLinks", "resolveAppLinks", "robots", "resolveRobots", "themeColor", "resolveThemeColor", "resolveAsArrayOrUndefined", "authors", "resolveItunes", "itunes", "other", "Object", "assign", "getDefinedMetadata", "mod", "props", "tracingProps", "isClientReference", "generateMetadata", "route", "parent", "getTracer", "trace", "ResolveMetadataSpan", "spanName", "attributes", "metadata", "collectStaticImagesFiles", "type", "undefined", "iconPromises", "map", "imageModule", "interopDefault", "length", "Promise", "all", "flat", "resolveStaticMetadata", "components", "staticMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "getComponentTypeModule", "getLayoutOrPageModule", "metadataExport", "push", "errorMod", "errorMetadataExport", "parentParams", "treePrefix", "getDynamicParamFromSegment", "searchParams", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "layerProps", "params", "filter", "s", "PAGE_SEGMENT_KEY", "join", "childTree", "keys", "commonOgKeys", "postProcessMetadata", "autoFillProps", "hasTwTitle", "absolute", "hasTwDescription", "description", "hasTwImages", "partialTwitter", "resolvedMetadata", "createDefaultMetadata", "resolvers", "generateMetadataResults", "resolvingIndex", "i", "j", "preloadMetadataExport", "resolve", "resolveParent", "generatedMetadata", "currentResolvedMetadata", "process", "env", "NODE_ENV", "freeze", "require", "cloneMetadata", "template", "resolvedMetadataItems", "error", "err"], "mappings": ";;;;;;;;;;;;;;;;;IA2TsBA,eAAe;eAAfA;;IAgDAC,oBAAoB;eAApBA;;IA0HAC,kBAAkB;eAAlBA;;IAqFAC,eAAe;eAAfA;;;iCA9iBgB;kCACW;8BACpB;uBACa;iCACR;8BAI3B;gCACwB;+BAUxB;8BACsB;wBACH;2BACU;4BACH;AAkBjC,SAASC,iBACPC,KAAwB,EACxBC,IAAsB;IAEtB,IAAI,CAACD,OAAO,OAAO;IACnB,IAAIC,SAAS,QAAQ;QACnB,0GAA0G;QAC1G,OAAO,CAAC,CACN,CAAA,OAAOD,UAAU,YACjBA,iBAAiBE,OACjBC,MAAMC,OAAO,CAACJ,UACbC,QAAQD,SAASA,KAAK,CAACC,KAAK;IAEjC,OAAO;QACL,4FAA4F;QAC5F,OAAO,CAAC,CAAE,CAAA,OAAOD,UAAU,YAAYC,QAAQD,SAASA,KAAK,CAACC,KAAK,AAAD;IACpE;AACF;AAEA,SAASI,oBACPC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B;QAedJ,iBAUEA;IAvBlB,IAAI,CAACE,qBAAqB;IAC1B,MAAM,EAAEG,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGP;IACtD,qFAAqF;IACrF,IACE,AAACG,QAAQ,CAACZ,iBAAiBO,0BAAAA,OAAQN,KAAK,EAAE,WACzCY,SAAS,CAACb,iBAAiBO,0BAAAA,OAAQN,KAAK,EAAE,UAC3C;QACAO,OAAOP,KAAK,GAAG;YACbW,MAAMA,QAAQ,EAAE;YAChBC,OAAOA,SAAS,EAAE;QACpB;IACF;IACA,8FAA8F;IAC9F,IAAIE,WAAW,EAACR,2BAAAA,kBAAAA,OAAQQ,OAAO,qBAAfR,gBAAiBU,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBC,IAAAA,gCAAc,EACpC;YAAE,GAAGX,OAAOO,OAAO;YAAEK,QAAQL;QAAQ,GACrCP,OAAOa,YAAY,EACnBV,eAAeI,OAAO;QAExBP,OAAOO,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACP,2BAAAA,oBAAAA,OAAQO,SAAS,qBAAjBP,kBAAmBU,cAAc,CAAC,YAAW;QAC7D,MAAMK,oBAAoBC,IAAAA,kCAAgB,EACxC;YAAE,GAAGf,OAAOM,SAAS;YAAEM,QAAQN;QAAU,GACzCN,OAAOa,YAAY,EACnBX,iBACAC,eAAeG,SAAS;QAE1BN,OAAOM,SAAS,GAAGQ;IACrB;IACA,IAAIN,UAAU;QACZR,OAAOQ,QAAQ,GAAGA;IACpB;IAEA,OAAOR;AACT;AAEA,+DAA+D;AAC/D,SAASgB,MAAM,EACbjB,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EAOhB;IACC,sFAAsF;IACtF,MAAMW,eACJ,QAAOd,0BAAAA,OAAQc,YAAY,MAAK,cAC5Bd,OAAOc,YAAY,GACnBb,OAAOa,YAAY;IACzB,IAAK,MAAMI,QAAQlB,OAAQ;QACzB,MAAMmB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZlB,OAAOmB,KAAK,GAAGC,IAAAA,0BAAY,EAACrB,OAAOoB,KAAK,EAAEhB,eAAegB,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBnB,OAAOqB,UAAU,GAAGC,IAAAA,gCAAiB,EACnCvB,OAAOsB,UAAU,EACjBR,cACAX;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOM,SAAS,GAAGS,IAAAA,kCAAgB,EACjChB,OAAOO,SAAS,EAChBO,cACAX,iBACAC,eAAeG,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdN,OAAOO,OAAO,GAAGI,IAAAA,gCAAc,EAC7BZ,OAAOQ,OAAO,EACdM,cACAV,eAAeI,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHP,OAAOuB,YAAY,GAAGC,IAAAA,kCAAmB,EAACzB,OAAOwB,YAAY;gBAC7D;YACF,KAAK;gBAAY;oBACfvB,OAAOyB,QAAQ,GAAGC,IAAAA,8BAAe,EAAC3B,OAAO0B,QAAQ;oBACjD;gBACF;YACA,KAAK;gBAAS;oBACZzB,OAAOP,KAAK,GAAGkC,IAAAA,0BAAY,EAAC5B,OAAON,KAAK;oBACxC;gBACF;YACA,KAAK;gBACHO,OAAO4B,WAAW,GAAGC,IAAAA,iCAAkB,EAAC9B,OAAO6B,WAAW;gBAC1D;YACF,KAAK;gBACH5B,OAAO8B,QAAQ,GAAGC,IAAAA,8BAAe,EAAChC,OAAO+B,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACb9B,OAAOgC,MAAM,GAAGC,IAAAA,4BAAa,EAAClC,OAAOiC,MAAM;oBAC3C;gBACF;YACA,KAAK;gBAAc;oBACjBhC,OAAOkC,UAAU,GAAGC,IAAAA,gCAAiB,EAACpC,OAAOmC,UAAU;oBACvD;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACflC,MAAM,CAACkB,IAAI,GAAGkB,IAAAA,gCAAyB,EAACrC,MAAM,CAACmB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdlB,MAAM,CAACkB,IAAI,GAAGkB,IAAAA,gCAAyB,EAACrC,OAAOsC,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACbrC,MAAM,CAACkB,IAAI,GAAGoB,IAAAA,4BAAa,EACzBvC,OAAOwC,MAAM,EACb1B,cACAX;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACkB,IAAI,GAAGnB,MAAM,CAACmB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHlB,OAAOwC,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAG1C,OAAOwC,KAAK,EAAEzC,OAAOyC,KAAK;gBAC3D;YACF,KAAK;gBACHxC,OAAOa,YAAY,GAAGA;gBACtB;YACF;gBACE;QACJ;IACF;IACAf,oBACEC,QACAC,QACAC,qBACAC,iBACAC;AAEJ;AAEA,eAAewC,mBACbC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,iFAAiF;IACjF,0EAA0E;IAC1E,IAAIC,IAAAA,kCAAiB,EAACH,MAAM;QAC1B,OAAO;IACT;IACA,IAAI,OAAOA,IAAII,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEC,KAAK,EAAE,GAAGH;QAClB,OAAO,CAACI,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACL,gBAAgB,EACpC;gBACEM,UAAU,CAAC,iBAAiB,EAAEL,MAAM,CAAC;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAML,IAAII,gBAAgB,CAACH,OAAOK;IAExC;IACA,OAAON,IAAIY,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAoC,EACpCX,KAAU,EACVa,IAAmD;QAU9C;IARL,IAAI,EAACF,4BAAAA,QAAU,CAACE,KAAK,GAAE,OAAOC;IAE9B,MAAMC,eAAeJ,QAAQ,CAACE,KAAyB,CAACG,GAAG,CACzD,OAAOC,cACLC,IAAAA,8BAAc,EAAC,MAAMD,YAAYjB;IAGrC,OAAOe,CAAAA,gCAAAA,aAAcI,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACN,kCAAnB,AAAC,MAAkCO,IAAI,KACvCR;AACN;AAEA,eAAeS,sBAAsBC,UAA0B,EAAExB,KAAU;IACzE,MAAM,EAAEW,QAAQ,EAAE,GAAGa;IACrB,IAAI,CAACb,UAAU,OAAO;IAEtB,MAAM,CAACpD,MAAMC,OAAOC,WAAWC,QAAQ,GAAG,MAAM0D,QAAQC,GAAG,CAAC;QAC1DT,yBAAyBD,UAAUX,OAAO;QAC1CY,yBAAyBD,UAAUX,OAAO;QAC1CY,yBAAyBD,UAAUX,OAAO;QAC1CY,yBAAyBD,UAAUX,OAAO;KAC3C;IAED,MAAMyB,iBAAiB;QACrBlE;QACAC;QACAC;QACAC;QACAC,UAAUgD,SAAShD,QAAQ;IAC7B;IAEA,OAAO8D;AACT;AAGO,eAAelF,gBAAgB,EACpCmF,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjB5B,KAAK,EACLI,KAAK,EACLyB,eAAe,EAQhB;IACC,IAAI9B;IACJ,IAAI+B;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnB9B,MAAM,MAAMkC,IAAAA,oCAAsB,EAACP,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACJ,CAAC9B,KAAK+B,QAAQ,GAAG,MAAMI,IAAAA,mCAAqB,EAACR;IAChD;IAEA,IAAII,SAAS;QACX1B,SAAS,CAAC,CAAC,EAAE0B,QAAQ,CAAC;IACxB;IAEA,MAAM1E,sBAAsB,MAAMmE,sBAAsBG,IAAI,CAAC,EAAE,EAAE1B;IACjE,MAAMmC,iBAAiBpC,MACnB,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEI;IAAM,KAC7C;IAEJuB,cAAcS,IAAI,CAAC;QAACD;QAAgB/E;KAAoB;IAExD,IAAI2E,+BAA+BF,iBAAiB;QAClD,MAAMQ,WAAW,MAAMJ,IAAAA,oCAAsB,EAACP,MAAMG;QACpD,MAAMS,sBAAsBD,WACxB,MAAMvC,mBAAmBuC,UAAUrC,OAAO;YAAEI;QAAM,KAClD;QACJwB,iBAAiB,CAAC,EAAE,GAAGU;QACvBV,iBAAiB,CAAC,EAAE,GAAGxE;IACzB;AACF;AAEO,eAAeZ,qBAAqB,EACzCkF,IAAI,EACJa,YAAY,EACZZ,aAAa,EACbC,iBAAiB,EACjBY,aAAa,EAAE,EACfC,0BAA0B,EAC1BC,YAAY,EACZb,eAAe,EAWhB;IACC,MAAM,CAACc,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAGnB;IAC5C,MAAMoB,oBAAoB;WAAIN;QAAYG;KAAQ;IAClD,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeP,2BAA2BE;IAChD;;GAEC,GACD,MAAMM,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGX,YAAY;QACf,CAACS,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEAX;IAEN,MAAMa,aAAa;QACjBC,QAAQJ;QACR,GAAIF,UAAU;YAAEL;QAAa,CAAC;IAChC;IAEA,MAAMnG,gBAAgB;QACpBmF;QACAC;QACAC;QACAC;QACA7B,OAAOoD;QACPhD,OAAO0C,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAMC,4BAAgB,EACpCC,IAAI,CAAC;IACV;IAEA,IAAK,MAAMpF,OAAOuE,eAAgB;QAChC,MAAMc,YAAYd,cAAc,CAACvE,IAAI;QACrC,MAAM7B,qBAAqB;YACzBkF,MAAMgC;YACN/B;YACAC;YACAW,cAAcU;YACdT,YAAYM;YACZJ;YACAD;YACAZ;QACF;IACF;IAEA,IAAIjC,OAAO+D,IAAI,CAACf,gBAAgBzB,MAAM,KAAK,KAAKU,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcS,IAAI,CAACR;IACrB;IAEA,OAAOD;AACT;AAEA,MAAMiC,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACPlD,QAA0B,EAC1BrD,cAA8B;IAE9B,MAAM,EAAEG,SAAS,EAAEC,OAAO,EAAE,GAAGiD;IAC/B,IAAIlD,WAAW;QACb,IAAIqG,gBAIC,CAAC;QACN,MAAMC,aAAarG,2BAAAA,QAASY,KAAK,CAAC0F,QAAQ;QAC1C,MAAMC,mBAAmBvG,2BAAAA,QAASwG,WAAW;QAC7C,MAAMC,cAAcnC,QAClBtE,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQK,MAAM;QAErD,IAAI,CAACgG,YAAYD,cAAcxF,KAAK,GAAGb,UAAUa,KAAK;QACtD,IAAI,CAAC2F,kBAAkBH,cAAcI,WAAW,GAAGzG,UAAUyG,WAAW;QACxE,IAAI,CAACC,aAAaL,cAAc/F,MAAM,GAAGN,UAAUM,MAAM;QAEzD,IAAI6B,OAAO+D,IAAI,CAACG,eAAe3C,MAAM,GAAG,GAAG;YACzC,MAAMiD,iBAAiBtG,IAAAA,gCAAc,EACnCgG,eACAnD,SAAS3C,YAAY,EACrBV,eAAeI,OAAO;YAExB,IAAIiD,SAASjD,OAAO,EAAE;gBACpBiD,SAASjD,OAAO,GAAGkC,OAAOC,MAAM,CAAC,CAAC,GAAGc,SAASjD,OAAO,EAAE;oBACrD,GAAI,CAACqG,cAAc;wBAAEzF,KAAK,EAAE8F,kCAAAA,eAAgB9F,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAAC2F,oBAAoB;wBACvBC,WAAW,EAAEE,kCAAAA,eAAgBF,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACC,eAAe;wBAAEpG,MAAM,EAAEqG,kCAAAA,eAAgBrG,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACL4C,SAASjD,OAAO,GAAG0G;YACrB;QACF;IACF;IACA,OAAOzD;AACT;AAEO,eAAelE,mBACpBkF,aAA4B,EAC5BtE,eAAgC;IAEhC,MAAMgH,mBAAmBC,IAAAA,sCAAqB;IAC9C,MAAMC,YAAmD,EAAE;IAC3D,MAAMC,0BAA4D,EAAE;IAEpE,IAAIlH,iBAAiC;QACnCgB,OAAO;QACPZ,SAAS;QACTD,WAAW;IACb;IAEA,uFAAuF;IACvF,yGAAyG;IAEzG,IAAIgH,iBAAiB;IACrB,IAAK,IAAIC,IAAI,GAAGA,IAAI/C,cAAcR,MAAM,EAAEuD,IAAK;QAC7C,MAAM,CAACvC,gBAAgB/E,oBAAoB,GAAGuE,aAAa,CAAC+C,EAAE;QAC9D,IAAI/D,WAA4B;QAChC,IAAI,OAAOwB,mBAAmB,YAAY;YACxC,IAAI,CAACoC,UAAUpD,MAAM,EAAE;gBACrB,IAAK,IAAIwD,IAAID,GAAGC,IAAIhD,cAAcR,MAAM,EAAEwD,IAAK;oBAC7C,MAAM,CAACC,sBAAsB,GAAGjD,aAAa,CAACgD,EAAE;oBAChD,6EAA6E;oBAC7E,IAAI,OAAOC,0BAA0B,YAAY;wBAC/CJ,wBAAwBpC,IAAI,CAC1BwC,sBACE,IAAIxD,QAAQ,CAACyD;4BACXN,UAAUnC,IAAI,CAACyC;wBACjB;oBAGN;gBACF;YACF;YAEA,MAAMC,gBAAgBP,SAAS,CAACE,eAAe;YAC/C,MAAMM,oBAAoBP,uBAAuB,CAACC,iBAAiB;YAEnE,uFAAuF;YACvF,qEAAqE;YACrE,MAAMO,0BACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBvF,OAAOwF,MAAM,CACXC,QAAQ,oBAAoBC,aAAa,CAACjB,qBAE5CA;YAEN,qFAAqF;YACrF,8FAA8F;YAC9F,mGAAmG;YACnGS,cAAcE;YACdrE,WACEoE,6BAA6B3D,UACzB,MAAM2D,oBACNA;QACR,OAAO,IAAI5C,mBAAmB,QAAQ,OAAOA,mBAAmB,UAAU;YACxE,yCAAyC;YACzCxB,WAAWwB;QACb;QAEAhE,MAAM;YACJd;YACAF,QAAQkH;YACRnH,QAAQyD;YACRvD;YACAE;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAIoH,IAAI/C,cAAcR,MAAM,GAAG,GAAG;gBAEvBkD,yBACIA,6BACFA;YAHX/G,iBAAiB;gBACfgB,OAAO+F,EAAAA,0BAAAA,iBAAiB/F,KAAK,qBAAtB+F,wBAAwBkB,QAAQ,KAAI;gBAC3C9H,WAAW4G,EAAAA,8BAAAA,iBAAiB5G,SAAS,qBAA1B4G,4BAA4B/F,KAAK,CAACiH,QAAQ,KAAI;gBACzD7H,SAAS2G,EAAAA,4BAAAA,iBAAiB3G,OAAO,qBAAxB2G,0BAA0B/F,KAAK,CAACiH,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,OAAO1B,oBAAoBQ,kBAAkB/G;AAC/C;AAEO,eAAeZ,gBAAgB,EACpCgF,IAAI,EACJa,YAAY,EACZZ,aAAa,EACbC,iBAAiB,EACjBa,0BAA0B,EAC1BC,YAAY,EACZb,eAAe,EACfxE,eAAe,EAYhB;IACC,MAAMmI,wBAAwB,MAAMhJ,qBAAqB;QACvDkF;QACAa;QACAZ;QACAC;QACAa;QACAC;QACAb;IACF;IACA,IAAIlB,WAA6B2D,IAAAA,sCAAqB;IACtD,IAAImB;IACJ,IAAI;QACF9E,WAAW,MAAMlE,mBAAmB+I,uBAAuBnI;IAC7D,EAAE,OAAOqI,KAAU;QACjBD,QAAQC;IACV;IACA,OAAO;QAAC/E;QAAU8E;KAAM;AAC1B"}