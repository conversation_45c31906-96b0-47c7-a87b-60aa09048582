{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["validateTurboNextConfig", "supportedTurbopackNextConfigOptions", "prodSpecificTurboNextConfigOptions", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "dim", "red", "underline", "yellow", "interopDefault", "isTTY", "process", "stdout", "turbopackGradient", "thankYouMessage", "join", "unsupportedParts", "babelrc", "path", "basename", "hasWebpack", "hasTurbo", "unsupportedConfig", "rawNextConfig", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isSupported", "some", "<PERSON><PERSON><PERSON>", "e", "console", "error", "hasWarningOrError", "log", "feedbackMessage", "warn", "map", "name", "pkgManager", "exit"], "mappings": ";;;;+BA2JsBA;;;eAAAA;;;6DA3JL;+DACM;2BAEkB;;;;;;AAEzC,MAAMC,sCAAsC;IAC1C,kCAAkC;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,0DAA0D;IAC1D;IACA;IACA;IAEA,+CAA+C;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,qDAAqD;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,oCAAoC;IACpC,kEAAkE;IAClE,yEAAyE;IACzE,4CAA4C;IAC5C;IACA;IACA,sBAAsB;IACtB,oCAAoC;IACpC,oBAAoB;IACpB,4BAA4B;IAC5B,+BAA+B;IAC/B,sCAAsC;IACtC,sCAAsC;IAEtC,yBAAyB;IACzB;IACA;IACA,gDAAgD;IAChD;IACA;CAsBD;AAED,kEAAkE;AAClE,MAAMC,qCAAqC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,eAAeF,wBAAwB,EAC5CG,GAAG,EACHC,KAAK,EAON;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,EAAE,GAC/CR,QAAQ;IACV,MAAM,EAAES,cAAc,EAAE,GACtBT,QAAQ;IAEV,kGAAkG;IAClG,MAAMU,QAAQC,QAAQC,MAAM,CAACF,KAAK;IAElC,MAAMG,oBAAoB,CAAC,EAAEV,KAC3BO,QACI,sVACA,iBACJ,CAAC,EAAEL,IAAI,UAAU,IAAI,CAAC;IAExB,IAAIS,kBACF;QACE;QACA;QACA;QACA;KACD,CAACC,IAAI,CAAC,QAAQ;IAEjB,IAAIC,mBAAmB;IACvB,IAAIC,UAAU,MAAMhB,mBAAmBJ;IACvC,IAAIoB,SAASA,UAAUC,aAAI,CAACC,QAAQ,CAACF;IAErC,IAAIG,aAAa;IACjB,IAAIC,WAAW;IAEf,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,IAAI;QACFA,gBAAgBd,eACd,MAAMe,IAAAA,eAAU,EAACC,mCAAwB,EAAE5B,KAAK;YAC9C6B,WAAW;QACb;QAGF,IAAI,OAAOH,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBE,mCAAwB,EAAE;gBAC/DvB;YACF;QACF;QAEA,MAAMyB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYJ;QAE/B,IAAImB,gBAAgB5C,QAChB;eACKH;eACAC;SACJ,GACDD;QAEJ,KAAK,MAAMoC,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,YAAY;gBAC7BvB,aAAa;YACf;YACA,IAAIW,IAAIY,UAAU,CAAC,uBAAuB;gBACxCtB,WAAW;YACb;YAEA,IAAIuB,cACFF,cAAcG,IAAI,CAAC,CAACC,eAAiBf,IAAIY,UAAU,CAACG,kBACpDR,aAAaf,eAAeQ,SAASO,aAAapC,eAAe6B;YACnE,IAAI,CAACa,aAAa;gBAChBtB,kBAAkBe,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOgB,GAAG;QACVC,QAAQC,KAAK,CAAC,mDAAmDF;IACnE;IAEA,MAAMG,oBAAoBjC,WAAWK,kBAAkBW,MAAM;IAC7D,IAAI,CAACiB,mBAAmB;QACtBpC,kBAAkBT,IAAIS;IACxB;IACAkC,QAAQG,GAAG,CAACtC,oBAAoBC;IAEhC,IAAIsC,kBAAkB,CAAC,4CAA4C,EAAE7C,UACnE,sCACA,EAAE,CAAC;IAEL,IAAIa,cAAc,CAACC,UAAU;QAC3B2B,QAAQK,IAAI,CACV,CAAC,EAAE,EAAE7C,OACH,YACA;EACN,EAAE,CAAC,sHAAsH,CAAC,CAAC,CAAC;IAE5H;IAEA,IAAIS,SAAS;QACXD,oBAAoB,CAAC,oBAAoB,EAAEZ,KACzCa,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEK,kBAAkBW,MAAM,KAAK,KAC7BX,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACA0B,QAAQK,IAAI,CACV,CAAC,EAAE,EAAE7C,OAAO,YAAY,CAAC,EAAEJ,KACzB,uCACA,uDAAuD,CAAC;IAE9D,OAAO,IAAIkB,kBAAkBW,MAAM,EAAE;QACnCjB,oBAAoB,CAAC,mDAAmD,EAAEZ,KACxE,kBACA,oEAAoE,EAAEkB,kBACrEgC,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAEjD,IAAIiD,MAAM,EAAE,CAAC,EACpCxC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIC,kBAAkB;QACpB,MAAMwC,aAAazD,cAAcF;QAEjCmD,QAAQC,KAAK,CACX,CAAC,4GAA4G,EAAEjC,iBAAiB;;;EAGpI,EAAEb,KACAC,KACE,CAAC,EACCoD,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLR,QAAQK,IAAI,CAACD;QAEbzC,QAAQ8C,IAAI,CAAC;IACf;IAEAT,QAAQG,GAAG,CAACC;IAEZ,OAAO7B;AACT"}