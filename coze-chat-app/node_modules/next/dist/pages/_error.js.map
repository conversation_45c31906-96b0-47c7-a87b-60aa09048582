{"version": 3, "sources": ["../../src/pages/_error.tsx"], "names": ["Error", "statusCodes", "_getInitialProps", "res", "err", "statusCode", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "React", "Component", "render", "withDarkMode", "props", "title", "div", "style", "Head", "dangerouslySetInnerHTML", "__html", "className", "displayName", "getInitialProps", "origGetInitialProps"], "mappings": ";;;;;;;eA8DqBA;;;;gEA9DH;+DACD;AAGjB,MAAMC,cAA0C;IAC9C,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAQA,SAASC,iBAAiB,KAGR;IAHQ,IAAA,EACxBC,GAAG,EACHC,GAAG,EACa,GAHQ;IAIxB,MAAMC,aACJF,OAAOA,IAAIE,UAAU,GAAGF,IAAIE,UAAU,GAAGD,MAAMA,IAAIC,UAAU,GAAI;IACnE,OAAO;QAAEA;IAAW;AACtB;AAEA,MAAMC,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,YAAY;IACd;IACAC,IAAI;QACFN,SAAS;QACTO,QAAQ;QACRC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,eAAe;IACjB;IACAC,IAAI;QACFH,UAAU;QACVC,YAAY;QACZL,YAAY;IACd;IACAQ,MAAM;QACJb,SAAS;IACX;AACF;AAKe,MAAMX,cAAsByB,cAAK,CAACC,SAAS;IAMxDC,SAAS;QACP,MAAM,EAAEtB,UAAU,EAAEuB,eAAe,IAAI,EAAE,GAAG,IAAI,CAACC,KAAK;QACtD,MAAMC,QACJ,IAAI,CAACD,KAAK,CAACC,KAAK,IAChB7B,WAAW,CAACI,WAAW,IACvB;QAEF,qBACE,6BAAC0B;YAAIC,OAAO1B,OAAOC,KAAK;yBACtB,6BAAC0B,aAAI,sBACH,6BAACH,eACEzB,aACG,AAAGA,aAAW,OAAIyB,QAClB,2EAGR,6BAACC;YAAIC,OAAO1B,OAAOS,IAAI;yBACrB,6BAACiB;YACCE,yBAAyB;gBACvB;;;;;;;;;;;;;;;;eAgBC,GACDC,QAAQ,AAAC,mGACPP,CAAAA,eACI,oIACA,EAAC;YAET;YAGDvB,2BACC,6BAACY;YAAGmB,WAAU;YAAgBJ,OAAO1B,OAAOW,EAAE;WAC3CZ,cAED,oBACJ,6BAAC0B;YAAIC,OAAO1B,OAAOkB,IAAI;yBACrB,6BAACD;YAAGS,OAAO1B,OAAOiB,EAAE;WACjB,IAAI,CAACM,KAAK,CAACC,KAAK,IAAIzB,aACnByB,sBAEA,4DAAE,2GAIF;IAOd;AACF;AAxEqB9B,MACZqC,cAAc;AADFrC,MAGZsC,kBAAkBpC;AAHNF,MAIZuC,sBAAsBrC"}